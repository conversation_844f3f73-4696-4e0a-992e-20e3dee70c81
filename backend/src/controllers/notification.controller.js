const notificationService = require('../services/notification.service');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class NotificationController {
  /**
   * Get member achievements
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAchievements(req, res) {
    try {
      const memberId = req.user.id; // From auth middleware

      const achievements = await notificationService.getMemberAchievements(memberId);

      res.json({
        success: true,
        data: {
          achievements,
          totalAchievements: achievements.length,
          totalPoints: achievements.reduce((sum, achievement) => sum + achievement.points, 0)
        }
      });
    } catch (error) {
      logger.error('Error in getAchievements:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get member notifications
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getNotifications(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const memberId = req.user.id; // From auth middleware
      const { limit = 50, unreadOnly = false } = req.query;

      const notifications = await notificationService.getMemberNotifications(
        memberId,
        parseInt(limit),
        unreadOnly === 'true'
      );

      res.json({
        success: true,
        data: {
          notifications,
          totalNotifications: notifications.length,
          unreadCount: notifications.filter(n => !n.isRead).length
        }
      });
    } catch (error) {
      logger.error('Error in getNotifications:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Mark notification as read
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async markNotificationAsRead(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { notificationId } = req.params;
      const memberId = req.user.id; // From auth middleware

      const success = await notificationService.markNotificationAsRead(
        parseInt(notificationId),
        memberId
      );

      if (success) {
        res.json({
          success: true,
          message: 'Notification marked as read'
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Notification not found'
        });
      }
    } catch (error) {
      logger.error('Error in markNotificationAsRead:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get member statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getStatistics(req, res) {
    try {
      const memberId = req.user.id; // From auth middleware

      const statistics = await notificationService.getMemberStatistics(memberId);

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      logger.error('Error in getStatistics:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get available achievements (for display purposes)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAvailableAchievements(req, res) {
    try {
      const memberId = req.user.id; // From auth middleware

      // Get all available achievements
      const availableAchievements = Object.values(notificationService.achievements);

      // Get earned achievements
      const earnedAchievements = await notificationService.getMemberAchievements(memberId);
      const earnedIds = earnedAchievements.map(a => a.achievementId);

      // Mark which achievements are earned
      const achievementsWithStatus = availableAchievements.map(achievement => ({
        ...achievement,
        isEarned: earnedIds.includes(achievement.id),
        earnedAt: earnedIds.includes(achievement.id) 
          ? earnedAchievements.find(a => a.achievementId === achievement.id).earnedAt 
          : null
      }));

      res.json({
        success: true,
        data: {
          achievements: achievementsWithStatus,
          totalAvailable: availableAchievements.length,
          totalEarned: earnedIds.length,
          completionPercentage: Math.round((earnedIds.length / availableAchievements.length) * 100)
        }
      });
    } catch (error) {
      logger.error('Error in getAvailableAchievements:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Mark all notifications as read
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async markAllNotificationsAsRead(req, res) {
    try {
      const memberId = req.user.id; // From auth middleware

      const { sequelize } = require('../models');
      
      await sequelize.query(`
        UPDATE member_notifications 
        SET isRead = TRUE 
        WHERE memberId = ? AND isRead = FALSE
      `, {
        replacements: [memberId]
      });

      res.json({
        success: true,
        message: 'All notifications marked as read'
      });
    } catch (error) {
      logger.error('Error in markAllNotificationsAsRead:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get unread notification count
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getUnreadCount(req, res) {
    try {
      const memberId = req.user.id; // From auth middleware

      const { sequelize } = require('../models');
      
      const [result] = await sequelize.query(`
        SELECT COUNT(*) as unreadCount
        FROM member_notifications 
        WHERE memberId = ? AND isRead = FALSE
      `, {
        replacements: [memberId]
      });

      res.json({
        success: true,
        data: {
          unreadCount: result[0]?.unreadCount || 0
        }
      });
    } catch (error) {
      logger.error('Error in getUnreadCount:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = new NotificationController(); 