const { Trainer<PERSON>onversation, Trainer<PERSON><PERSON>, Trainer, Member, TrainerSubmodule, OpenAIThread, sequelize } = require('../models');
const config = require('../config');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');
const webSocketService = require('./websocket.service');
const ChatOpenAIService = require('./chat/openai.service');
const OpenAIAssistantService = require('./openai/assistant.service');
const notificationService = require('./notification.service');

class ChatService {
  constructor() {
    this.openaiService = new ChatOpenAIService();
    this.assistantService = new OpenAIAssistantService();
  }

  /**
   * Initialize conversation session with OpenAI Assistant
   * @param {number} trainerId - Trainer ID
   * @param {number} submoduleId - Submodule ID (optional)
   * @param {number} memberId - Member ID
   * @returns {Promise<Object>} - Session initialization result
   */
  async initializeAssistantConversation(trainerId, submoduleId, memberId) {
    try {
      // Verify trainer and member relationship
      const trainer = await Trainer.findOne({
        where: { id: trainerId, status: 'active' },
        include: [
          {
            model: Member,
            as: 'members',
            where: { id: memberId, isActive: true },
            through: { attributes: [] }
          },
          {
            model: TrainerSubmodule,
            as: 'submodules',
            where: submoduleId ? { id: submoduleId, status: 'active' } : { status: 'active' },
            order: [['orderIndex', 'ASC']]
          }
        ]
      });

      if (!trainer) {
        throw new Error('Trainer not found, inactive, or not assigned to member');
      }

      // Use provided submodule or first available submodule
      const targetSubmodule = submoduleId 
        ? trainer.submodules.find(s => s.id === submoduleId)
        : trainer.submodules[0];

      if (!targetSubmodule) {
        throw new Error('No active submodules found for this trainer');
      }

      // Create or get existing thread
      const threadResult = await this.assistantService.createThread(
        trainerId,
        targetSubmodule.id,
        memberId
      );

      // Get conversation history
      const conversationHistory = await this.assistantService.getConversationHistory(
        threadResult.thread.id,
        20
      );

      // Get current progress
      const currentProgress = await TrainerScore.findOne({
        where: { 
          trainerId, 
          memberId,
          submoduleId: targetSubmodule.id 
        }
      });

      // Get thread statistics
      const threadStats = await this.assistantService.getThreadStats(threadResult.thread.id);

      logger.info(`Assistant conversation initialized: trainer ${trainerId}, submodule ${targetSubmodule.id}, member ${memberId}`);

      return {
        success: true,
        thread: {
          id: threadResult.thread.id,
          threadId: threadResult.thread.threadId,
          isNew: threadResult.isNew
        },
        trainer: {
          id: trainer.id,
          name: trainer.name,
          description: trainer.description
        },
        submodule: {
          id: targetSubmodule.id,
          name: targetSubmodule.name,
          description: targetSubmodule.description,
          orderIndex: targetSubmodule.orderIndex
        },
        availableSubmodules: trainer.submodules.map(s => ({
          id: s.id,
          name: s.name,
          orderIndex: s.orderIndex
        })),
        conversationHistory,
        currentProgress: currentProgress?.progress || 0,
        currentScore: currentProgress?.score || null,
        threadStats
      };
    } catch (error) {
      logger.error('Error initializing assistant conversation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send message to OpenAI Assistant
   * @param {number} threadId - Internal thread ID
   * @param {string} message - User message
   * @param {number} memberId - Member ID
   * @returns {Promise<Object>} - Assistant response
   */
  async sendMessageToAssistant(threadId, message, memberId) {
    try {
      // Get thread info
      const thread = await OpenAIThread.findByPk(threadId, {
        include: [
          {
            model: Trainer,
            as: 'trainer'
          },
          {
            model: TrainerSubmodule,
            as: 'submodule'
          },
          {
            model: Member,
            as: 'member'
          }
        ]
      });

      if (!thread) {
        throw new Error('Thread not found');
      }

      // Send message to assistant
      const response = await this.assistantService.sendMessage(threadId, message, memberId);

      // Update progress tracking if progress or score provided
      if (response.progress !== null || response.score !== null) {
        await this.updateProgress(
          thread.trainerId,
          memberId,
          response.progress,
          response.score,
          thread.submoduleId
        );
      }

      // Broadcast real-time update to monitoring admins
      if (webSocketService.io) {
        webSocketService.broadcastAssistantConversationMessage({
          threadId: thread.id,
          trainerId: thread.trainerId,
          submoduleId: thread.submoduleId,
          memberId,
          message: response.assistantResponse.content,
          progress: response.progress,
          score: response.score,
          adminId: thread.trainer.adminId
        });

        // Send monitoring update for live dashboard
        webSocketService.sendMonitoringUpdate('assistant-conversation', {
          adminId: thread.trainer.adminId,
          trainerId: thread.trainerId,
          trainerName: thread.trainer.name,
          submoduleId: thread.submoduleId,
          submoduleName: thread.submodule.name,
          memberId,
          memberName: thread.member.name,
          messageType: 'assistant',
          progress: response.progress,
          score: response.score,
          threadId: thread.threadId
        });

        // Broadcast progress update if available
        if (response.progress !== null || response.score !== null) {
          webSocketService.broadcastAssistantProgressUpdate({
            threadId: thread.id,
            trainerId: thread.trainerId,
            submoduleId: thread.submoduleId,
            memberId,
            progress: response.progress,
            score: response.score,
            adminId: thread.trainer.adminId
          });

          // Send progress notification
          await notificationService.sendProgressNotification(memberId, {
            trainerId: thread.trainerId,
            submoduleId: thread.submoduleId,
            progress: response.progress,
            score: response.score
          });

          // Check for progress achievements
          if (response.progress !== null) {
            await notificationService.checkAchievements(memberId, 'progress_updated', {
              progress: response.progress
            });
          }

          // Check for score achievements
          if (response.score !== null) {
            await notificationService.checkAchievements(memberId, 'score_received', {
              score: response.score
            });
          }
        }

        // Check for message achievements
        await notificationService.checkAchievements(memberId, 'message_sent', {
          threadId: thread.id,
          trainerId: thread.trainerId
        });
      }

      logger.info(`Assistant message processed: thread ${threadId}, progress ${response.progress}%`);

      return {
        success: true,
        userMessage: {
          id: response.userMessage.id,
          content: response.userMessage.content,
          timestamp: response.userMessage.created_at,
          role: 'user'
        },
        assistantResponse: {
          id: response.assistantResponse.id,
          content: response.assistantResponse.content,
          progress: response.progress,
          score: response.score,
          timestamp: response.assistantResponse.created_at,
          role: 'assistant'
        },
        threadStats: await this.assistantService.getThreadStats(threadId)
      };
    } catch (error) {
      logger.error('Error sending message to assistant:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Switch to different submodule within same trainer
   * @param {number} currentThreadId - Current thread ID
   * @param {number} newSubmoduleId - New submodule ID
   * @param {number} memberId - Member ID
   * @returns {Promise<Object>} - Switch result
   */
  async switchSubmodule(currentThreadId, newSubmoduleId, memberId) {
    try {
      // Verify current thread belongs to member
      const currentThread = await OpenAIThread.findOne({
        where: { 
          id: currentThreadId, 
          memberId,
          status: 'active'
        },
        include: [
          {
            model: Trainer,
            as: 'trainer'
          }
        ]
      });

      if (!currentThread) {
        throw new Error('Current thread not found or access denied');
      }

      // Verify new submodule exists and belongs to same trainer
      const newSubmodule = await TrainerSubmodule.findOne({
        where: {
          id: newSubmoduleId,
          trainerId: currentThread.trainerId,
          status: 'active'
        }
      });

      if (!newSubmodule) {
        throw new Error('New submodule not found or not active');
      }

      // Switch submodule using assistant service
      const switchResult = await this.assistantService.switchSubmodule(
        currentThreadId,
        newSubmoduleId
      );

      // Get conversation history for new thread
      const conversationHistory = await this.assistantService.getConversationHistory(
        switchResult.newThread.id,
        20
      );

      // Get current progress for new submodule
      const currentProgress = await TrainerScore.findOne({
        where: { 
          trainerId: currentThread.trainerId, 
          memberId,
          submoduleId: newSubmoduleId
        }
      });

      logger.info(`Submodule switched: member ${memberId}, from thread ${currentThreadId} to ${switchResult.newThread.id}`);

      return {
        success: true,
        oldThread: {
          id: switchResult.oldThread.id,
          threadId: switchResult.oldThread.threadId,
          status: switchResult.oldThread.status
        },
        newThread: {
          id: switchResult.newThread.id,
          threadId: switchResult.newThread.threadId,
          status: switchResult.newThread.status
        },
        submodule: {
          id: newSubmodule.id,
          name: newSubmodule.name,
          description: newSubmodule.description,
          orderIndex: newSubmodule.orderIndex
        },
        conversationHistory,
        currentProgress: currentProgress?.progress || 0,
        currentScore: currentProgress?.score || null
      };
    } catch (error) {
      logger.error('Error switching submodule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get member's conversation history across all threads
   * @param {number} memberId - Member ID
   * @param {number} trainerId - Trainer ID (optional)
   * @param {number} limit - Message limit
   * @returns {Promise<Object>} - Conversation history
   */
  async getMemberConversationHistory(memberId, trainerId = null, limit = 100) {
    try {
      const whereClause = { memberId };
      if (trainerId) {
        whereClause.trainerId = trainerId;
      }

      const threads = await OpenAIThread.findAll({
        where: whereClause,
        include: [
          {
            model: Trainer,
            as: 'trainer',
            attributes: ['id', 'name']
          },
          {
            model: TrainerSubmodule,
            as: 'submodule',
            attributes: ['id', 'name', 'orderIndex']
          },
          {
            model: OpenAIAssistantService.Message, // Assuming OpenAIAssistantService.Message is the correct model for messages
            as: 'messages',
            limit: limit,
            order: [['created_at', 'DESC']]
          }
        ],
        order: [['lastMessageAt', 'DESC']]
      });

      const conversationHistory = threads.map(thread => ({
        threadId: thread.id,
        openaiThreadId: thread.threadId,
        trainer: thread.trainer,
        submodule: thread.submodule,
        status: thread.status,
        lastMessageAt: thread.lastMessageAt,
        messageCount: thread.messages.length,
        messages: thread.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          role: msg.role,
          progress: msg.aiProgress,
          score: msg.aiScore,
          timestamp: msg.created_at
        }))
      }));

      return {
        success: true,
        conversationHistory,
        totalThreads: threads.length
      };
    } catch (error) {
      logger.error('Error getting member conversation history:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get progress tracking for member across all submodules
   * @param {number} memberId - Member ID
   * @param {number} trainerId - Trainer ID (optional)
   * @returns {Promise<Object>} - Progress tracking data
   */
  async getMemberProgressTracking(memberId, trainerId = null) {
    try {
      const whereClause = { memberId };
      if (trainerId) {
        whereClause.trainerId = trainerId;
      }

      const progressData = await TrainerScore.findAll({
        where: whereClause,
        include: [
          {
            model: Trainer,
            as: 'trainer',
            attributes: ['id', 'name']
          },
          {
            model: TrainerSubmodule,
            as: 'submodule',
            attributes: ['id', 'name', 'orderIndex']
          }
        ],
        order: [['trainer', 'id'], ['submodule', 'orderIndex']]
      });

      const progressByTrainer = {};
      
      progressData.forEach(progress => {
        const trainerId = progress.trainer.id;
        const trainerName = progress.trainer.name;
        
        if (!progressByTrainer[trainerId]) {
          progressByTrainer[trainerId] = {
            trainerId,
            trainerName,
            submodules: [],
            overallProgress: 0,
            completedSubmodules: 0,
            totalSubmodules: 0
          };
        }
        
        progressByTrainer[trainerId].submodules.push({
          submoduleId: progress.submodule?.id,
          submoduleName: progress.submodule?.name,
          orderIndex: progress.submodule?.orderIndex,
          progress: progress.progress,
          score: progress.score,
          isCompleted: progress.isCompleted,
          completedAt: progress.completedAt,
          lastUpdated: progress.updated_at
        });
        
        progressByTrainer[trainerId].totalSubmodules++;
        if (progress.isCompleted) {
          progressByTrainer[trainerId].completedSubmodules++;
        }
      });

      // Calculate overall progress for each trainer
      Object.values(progressByTrainer).forEach(trainer => {
        const totalProgress = trainer.submodules.reduce((sum, sub) => sum + sub.progress, 0);
        trainer.overallProgress = trainer.totalSubmodules > 0 
          ? Math.round(totalProgress / trainer.totalSubmodules) 
          : 0;
      });

      return {
        success: true,
        progressByTrainer: Object.values(progressByTrainer),
        summary: {
          totalTrainers: Object.keys(progressByTrainer).length,
          totalSubmodules: progressData.length,
          completedSubmodules: progressData.filter(p => p.isCompleted).length,
          averageProgress: progressData.length > 0 
            ? Math.round(progressData.reduce((sum, p) => sum + p.progress, 0) / progressData.length)
            : 0
        }
      };
    } catch (error) {
      logger.error('Error getting member progress tracking:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Initialize conversation session (legacy method for backward compatibility)
   * @param {number} trainerId - Trainer ID
   * @param {number} memberId - Member ID
   * @returns {Promise<Object>} - Session initialization result
   */
  async initializeConversation(trainerId, memberId) {
    const transaction = await sequelize.transaction();
    
    try {
      // Verify trainer and member relationship
      const trainer = await Trainer.findOne({
        where: { id: trainerId, status: 'active' },
        include: [
          {
            model: Member,
            as: 'members',
            where: { id: memberId, isActive: true },
            through: { attributes: [] }
          }
        ]
      });

      if (!trainer) {
        throw new Error('Trainer not found, inactive, or not assigned to member');
      }

      // Generate session ID
      const sessionId = uuidv4();

      // Get existing conversation context
      const existingConversations = await TrainerConversation.findAll({
        where: { trainerId, memberId },
        order: [['created_at', 'ASC']],
        limit: 20 // Last 20 messages for context
      });

      // Get current progress
      const currentProgress = await TrainerScore.findOne({
        where: { trainerId, memberId }
      });

      // Create initial system message if first conversation
      if (existingConversations.length === 0) {
        const systemMessage = this.buildSystemMessage(trainer, currentProgress);
        
        await TrainerConversation.create({
          trainerId,
          memberId,
          messageFrom: 'ai',
          message: systemMessage,
          sessionId,
          metadata: {
            type: 'system_initialization',
            trainerName: trainer.name
          }
        }, { transaction });
      }

      await transaction.commit();

      logger.info(`Conversation initialized: trainer ${trainerId}, member ${memberId}, session ${sessionId}`);

      return {
        success: true,
        sessionId,
        trainer: {
          id: trainer.id,
          name: trainer.name,
          description: trainer.description
        },
        conversationHistory: existingConversations,
        currentProgress: currentProgress?.progress || 0,
        currentScore: currentProgress?.score || null
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error initializing conversation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send message to AI trainer (legacy method for backward compatibility)
   * @param {number} trainerId - Trainer ID
   * @param {number} memberId - Member ID
   * @param {string} message - User message
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} - AI response
   */
  async sendMessage(trainerId, memberId, message, sessionId) {
    const transaction = await sequelize.transaction();
    
    try {
      // Verify session and permissions
      const trainer = await Trainer.findOne({
        where: { id: trainerId, status: 'active' },
        include: [
          {
            model: Member,
            as: 'members',
            where: { id: memberId, isActive: true },
            through: { attributes: [] }
          }
        ]
      });

      if (!trainer) {
        throw new Error('Trainer not found, inactive, or not assigned to member');
      }

      // Save user message
      const userMessage = await TrainerConversation.create({
        trainerId,
        memberId,
        messageFrom: 'member',
        message,
        sessionId,
        metadata: {
          timestamp: new Date().toISOString(),
          messageLength: message.length
        }
      }, { transaction });

      // Get conversation history for context
      const conversationHistory = await TrainerConversation.findAll({
        where: { trainerId, memberId },
        order: [['created_at', 'ASC']],
        limit: 30 // Last 30 messages for context
      });

      // Get current progress
      const currentProgress = await TrainerScore.findOne({
        where: { trainerId, memberId }
      });

      // Generate AI response
      const aiResponse = await this.openaiService.generateAIResponse(
        trainer, 
        conversationHistory, 
        currentProgress,
        this.buildConversationContext.bind(this)
      );

      // Parse AI response for progress and score
      const parsedResponse = this.openaiService.parseAIResponse(
        aiResponse,
        this.validateProgress.bind(this),
        this.validateScore.bind(this)
      );

      // Save AI message
      const aiMessage = await TrainerConversation.create({
        trainerId,
        memberId,
        messageFrom: 'ai',
        message: parsedResponse.message,
        aiProgress: parsedResponse.progress,
        aiScore: parsedResponse.score,
        sessionId,
        metadata: {
          timestamp: new Date().toISOString(),
          openaiModel: config.openai.model,
          tokensUsed: parsedResponse.tokensUsed,
          processingTime: parsedResponse.processingTime
        }
      }, { transaction });

      // Update progress tracking
      if (parsedResponse.progress !== null || parsedResponse.score !== null) {
        await this.updateProgress(
          trainerId, 
          memberId, 
          parsedResponse.progress, 
          parsedResponse.score,
          null,
          transaction
        );
      }

      await transaction.commit();

      // Broadcast real-time update to monitoring admins
      if (webSocketService.io) {
        webSocketService.broadcastConversationMessage({
          trainerId,
          memberId,
          message: parsedResponse.message,
          progress: parsedResponse.progress,
          score: parsedResponse.score,
          adminId: trainer.adminId
        });
      }

      logger.info(`Message processed: trainer ${trainerId}, member ${memberId}, progress ${parsedResponse.progress}%`);

      return {
        success: true,
        response: {
          id: aiMessage.id,
          message: parsedResponse.message,
          progress: parsedResponse.progress,
          score: parsedResponse.score,
          timestamp: aiMessage.created_at,
          sessionId
        },
        userMessage: {
          id: userMessage.id,
          message: userMessage.message,
          timestamp: userMessage.created_at
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error sending message:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate AI response using OpenAI API
   * @param {Object} trainer - Trainer object
   * @param {Array} conversationHistory - Conversation history
   * @param {Object} currentProgress - Current progress data
   * @returns {Promise<string>} - AI response
   */
  async generateAIResponse(trainer, conversationHistory, currentProgress) {
    const startTime = Date.now();
    
    try {
      // Build conversation context
      const messages = this.buildConversationContext(trainer, conversationHistory, currentProgress);

      // Call OpenAI API with retry logic
      const response = await this.openaiService.callOpenAIWithRetry(messages);

      const processingTime = Date.now() - startTime;
      
      return {
        content: response.choices[0].message.content,
        tokensUsed: response.usage?.total_tokens || 0,
        processingTime
      };
    } catch (error) {
      logger.error('Error generating AI response:', error);
      
      // Fallback response
      return {
        content: JSON.stringify({
          message: "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
          progress: currentProgress?.progress || 0,
          score: null
        }),
        tokensUsed: 0,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Call OpenAI API with circuit breaker and retry logic
   * @param {Array} messages - Messages array
   * @returns {Promise<Object>} - OpenAI response
   */
  async callOpenAIWithRetry(messages) {
    const openaiCall = async () => {
      return await this.openaiService.callOpenAIWithRetry(messages);
    };

    // Execute with circuit breaker and retry logic
    return await this.openaiService.executeWithCircuitBreakerAndRetry(openaiCall);
  }

  /**
   * Build conversation context for OpenAI
   * @param {Object} trainer - Trainer object
   * @param {Array} conversationHistory - Conversation history
   * @param {Object} currentProgress - Current progress data
   * @returns {Array} - Messages array for OpenAI
   */
  buildConversationContext(trainer, conversationHistory, currentProgress) {
    const messages = [];

    // System message with trainer prompt and progress instructions
    const systemPrompt = this.buildSystemMessage(trainer, currentProgress);
    messages.push({
      role: 'system',
      content: systemPrompt
    });

    // Add conversation history
    conversationHistory.forEach(conv => {
      if (conv.messageFrom === 'member') {
        messages.push({
          role: 'user',
          content: conv.message
        });
      } else if (conv.messageFrom === 'ai' && conv.message) {
        // Parse AI message to get clean content
        try {
          const parsed = JSON.parse(conv.message);
          messages.push({
            role: 'assistant',
            content: JSON.stringify(parsed)
          });
        } catch {
          messages.push({
            role: 'assistant',
            content: conv.message
          });
        }
      }
    });

    return messages;
  }

  /**
   * Build system message with trainer prompt and progress instructions
   * @param {Object} trainer - Trainer object
   * @param {Object} currentProgress - Current progress data
   * @returns {string} - System message
   */
  buildSystemMessage(trainer, currentProgress) {
    const progressInfo = currentProgress ? 
      `Current progress: ${currentProgress.progress}%, Current score: ${currentProgress.score || 'Not scored yet'}` :
      'Current progress: 0%, Current score: Not scored yet';

    return `${trainer.systemPrompt}

CRITICAL INSTRUCTIONS:
1. You MUST respond in valid JSON format with exactly these fields:
   {
     "message": "Your response to the user",
     "progress": number (0-100, current progress percentage),
     "score": number or null (0-10 scale, only if you're evaluating performance)
   }

2. Progress tracking:
   - Start at 0% for new conversations
   - Increment progress based on learning milestones
   - ${progressInfo}
   - Only update progress when genuine learning occurs
   - Progress should reflect cumulative understanding

3. Scoring guidelines:
   - Only provide scores when evaluating specific exercises or assessments
   - Use 0-10 scale (0 = poor, 10 = excellent)
   - Set score to null for regular conversation

4. Your response should be educational, encouraging, and aligned with the training objectives.

Remember: Your response must be valid JSON format only.`;
  }

  /**
   * Parse AI response to extract message, progress, and score
   * @param {Object} aiResponse - AI response object
   * @returns {Object} - Parsed response
   */
  parseAIResponse(aiResponse) {
    try {
      const parsed = JSON.parse(aiResponse.content);
      
      return {
        message: parsed.message || 'Invalid response format',
        progress: this.validateProgress(parsed.progress),
        score: this.validateScore(parsed.score),
        tokensUsed: aiResponse.tokensUsed,
        processingTime: aiResponse.processingTime
      };
    } catch (error) {
      logger.error('Error parsing AI response:', error);
      
      // Fallback parsing
      return {
        message: aiResponse.content || 'Invalid response received',
        progress: null,
        score: null,
        tokensUsed: aiResponse.tokensUsed,
        processingTime: aiResponse.processingTime
      };
    }
  }

  /**
   * Validate progress value
   * @param {any} progress - Progress value to validate
   * @returns {number|null} - Validated progress
   */
  validateProgress(progress) {
    if (progress === null || progress === undefined) {
      return null;
    }
    
    const numProgress = Number(progress);
    if (isNaN(numProgress)) {
      return null;
    }
    
    return Math.max(0, Math.min(100, Math.round(numProgress)));
  }

  /**
   * Validate score value
   * @param {any} score - Score value to validate
   * @returns {number|null} - Validated score
   */
  validateScore(score) {
    if (score === null || score === undefined) {
      return null;
    }
    
    const numScore = Number(score);
    if (isNaN(numScore)) {
      return null;
    }
    
    return Math.max(0, Math.min(10, Math.round(numScore * 10) / 10));
  }

  /**
   * Update progress tracking
   * @param {number} trainerId - Trainer ID
   * @param {number} memberId - Member ID
   * @param {number} progress - Progress percentage
   * @param {number} score - Score value
   * @param {number} submoduleId - Submodule ID (optional)
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Promise<void>}
   */
  async updateProgress(trainerId, memberId, progress, score, submoduleId = null, transaction = null) {
    try {
      const updateData = {};
      
      if (progress !== null) {
        updateData.progress = progress;
        
        // Mark as completed if progress reaches 100%
        if (progress >= 100) {
          updateData.isCompleted = true;
          updateData.completedAt = new Date();
        }
      }
      
      if (score !== null) {
        updateData.score = score;
      }
      
      if (submoduleId) {
        updateData.submoduleId = submoduleId;
      }
      
      updateData.updated_at = new Date();

      const whereClause = { trainerId, memberId };
      if (submoduleId) {
        whereClause.submoduleId = submoduleId;
      }

      await TrainerScore.upsert({
        ...whereClause,
        ...updateData
      }, { transaction });

      logger.info(`Progress updated: trainer ${trainerId}, member ${memberId}, submodule ${submoduleId}, progress ${progress}%, score ${score}`);
    } catch (error) {
      logger.error('Error updating progress:', error);
      throw error;
    }
  }

  /**
   * Get conversation history
   * @param {number} trainerId - Trainer ID
   * @param {number} memberId - Member ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Conversation history
   */
  async getConversationHistory(trainerId, memberId, options = {}) {
    try {
      const { page = 1, limit = 50, sessionId } = options;
      const offset = (page - 1) * limit;

      const whereClause = { trainerId, memberId };
      if (sessionId) {
        whereClause.sessionId = sessionId;
      }

      const { count, rows: conversations } = await TrainerConversation.findAndCountAll({
        where: whereClause,
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset),
        include: [
          {
            model: Trainer,
            as: 'trainer',
            attributes: ['id', 'name']
          },
          {
            model: Member,
            as: 'member',
            attributes: ['id', 'name']
          }
        ]
      });

      return {
        success: true,
        conversations: conversations.reverse(), // Reverse to show chronological order
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting conversation history:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get active sessions for monitoring
   * @param {number} adminId - Admin ID
   * @returns {Promise<Object>} - Active sessions
   */
  async getActiveSessions(adminId) {
    try {
      const activeSessions = await sequelize.query(`
        SELECT DISTINCT
          tc.sessionId,
          tc.trainerId,
          tc.memberId,
          t.name as trainerName,
          m.name as memberName,
          m.email as memberEmail,
          MAX(tc.created_at) as lastActivity,
          COUNT(tc.id) as messageCount
        FROM trainer_conversations tc
        JOIN trainers t ON tc.trainerId = t.id
        JOIN members m ON tc.memberId = m.id
        WHERE t.adminId = :adminId
          AND tc.sessionId IS NOT NULL
          AND tc.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY tc.sessionId, tc.trainerId, tc.memberId, t.name, m.name, m.email
        ORDER BY lastActivity DESC
      `, {
        replacements: { adminId },
        type: sequelize.QueryTypes.SELECT
      });

      return {
        success: true,
        sessions: activeSessions
      };
    } catch (error) {
      logger.error('Error getting active sessions:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Sleep utility for retry logic
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = ChatService; 