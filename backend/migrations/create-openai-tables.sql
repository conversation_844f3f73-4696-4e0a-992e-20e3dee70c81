-- Migration: Create OpenAI Assistant tables
-- Created: 2024-01-XX
-- Description: Creates tables for OpenAI Assistant API integration

-- Create openai_threads table
CREATE TABLE IF NOT EXISTS openai_threads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    threadId VARCHAR(255) NOT NULL UNIQUE,
    trainerId INT NOT NULL,
    submoduleId INT NULL,
    memberId INT NOT NULL,
    assistantId VARCHAR(255) NULL,
    status ENUM('active', 'inactive', 'archived') NOT NULL DEFAULT 'active',
    lastMessageAt DATETIME NULL,
    metadata JSON NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_threadId (threadId),
    INDEX idx_trainer_member (trainerId, memberId),
    INDEX idx_submodule (submoduleId),
    INDEX idx_status (status),
    INDEX idx_lastMessageAt (lastMessageAt),
    
    FOR<PERSON><PERSON><PERSON> KEY (trainerId) REFERENCES trainers(id) ON DELETE CASCADE,
    FOREIGN KEY (submoduleId) REFERENCES trainer_submodules(id) ON DELETE SET NULL,
    FOREIGN KEY (memberId) REFERENCES members(id) ON DELETE CASCADE
);

-- Create openai_messages table
CREATE TABLE IF NOT EXISTS openai_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    messageId VARCHAR(255) NOT NULL UNIQUE,
    threadId INT NOT NULL,
    openaiThreadId VARCHAR(255) NOT NULL,
    runId VARCHAR(255) NULL,
    role ENUM('user', 'assistant') NOT NULL,
    content TEXT NOT NULL,
    aiProgress INT NULL CHECK (aiProgress >= 0 AND aiProgress <= 100),
    aiScore DECIMAL(3,1) NULL CHECK (aiScore >= 0 AND aiScore <= 10),
    status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    metadata JSON NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_messageId (messageId),
    INDEX idx_threadId (threadId),
    INDEX idx_openaiThreadId (openaiThreadId),
    INDEX idx_runId (runId),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (threadId) REFERENCES openai_threads(id) ON DELETE CASCADE
);

-- Add indexes for performance
CREATE INDEX idx_openai_threads_composite ON openai_threads(trainerId, memberId, status);
CREATE INDEX idx_openai_messages_composite ON openai_messages(threadId, role, created_at);
CREATE INDEX idx_openai_messages_progress ON openai_messages(aiProgress) WHERE aiProgress IS NOT NULL;
CREATE INDEX idx_openai_messages_score ON openai_messages(aiScore) WHERE aiScore IS NOT NULL;

-- Update trainer_scores table to include submoduleId if not exists
ALTER TABLE trainer_scores 
ADD COLUMN IF NOT EXISTS submoduleId INT NULL AFTER trainerId,
ADD FOREIGN KEY IF NOT EXISTS (submoduleId) REFERENCES trainer_submodules(id) ON DELETE SET NULL;

-- Create index for submoduleId in trainer_scores
CREATE INDEX IF NOT EXISTS idx_trainer_scores_submodule ON trainer_scores(submoduleId);

-- Insert sample data (optional - for testing)
-- You can uncomment this section for testing purposes

/*
-- Sample trainer and submodule (assuming they exist)
INSERT IGNORE INTO trainer_submodules (trainerId, name, systemPrompt, orderIndex, status, description) VALUES
(1, 'Introduction to AI', 'You are an AI trainer helping students learn about artificial intelligence basics. Be encouraging and provide step-by-step explanations.', 0, 'active', 'Basic AI concepts and terminology'),
(1, 'Machine Learning Fundamentals', 'You are an AI trainer specializing in machine learning. Help students understand core ML concepts with practical examples.', 1, 'active', 'Introduction to machine learning algorithms'),
(1, 'Advanced AI Topics', 'You are an advanced AI trainer. Guide students through complex AI topics and real-world applications.', 2, 'active', 'Deep learning and neural networks');
*/ 