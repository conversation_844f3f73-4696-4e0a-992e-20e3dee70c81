'use client';

import { useEffect, useState, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { api } from '@/lib/api';
import { Trainer } from '@/types';
import { useWebSocket } from '@/hooks/useWebSocket';
import {
  Send,
  Bot,
  User,
  Loader2,
  ArrowLeft,
  Star
} from 'lucide-react';
import Link from 'next/link';

interface Message {
  id: number;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: string;
  isLoading?: boolean;
  progress?: number;
  score?: number;
}

export default function MemberChatPage() {
  const searchParams = useSearchParams();
  const trainerId = searchParams.get('trainer');
  const { user } = useAuthStore();
  const { socket, isConnected, on, off } = useWebSocket();

  const [trainer, setTrainer] = useState<Trainer | null>(null);
  const [assignedTrainers, setAssignedTrainers] = useState<Trainer[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTrainer, setSelectedTrainer] = useState<number | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [typingIndicator, setTypingIndicator] = useState(false);


  useEffect(() => {
    if (trainerId) {
      setSelectedTrainer(parseInt(trainerId));
      fetchTrainer(parseInt(trainerId));
      initializeChat(parseInt(trainerId));
    }
  }, [trainerId]);

  const fetchAssignedTrainers = useCallback(async () => {
    try {
      if (user?.id) {
        const response = await api.members.getAssignedTrainers(user.id);
        setAssignedTrainers(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching assigned trainers:', error);
    }
  }, [user?.id]);

  useEffect(() => {
    fetchAssignedTrainers();
  }, [fetchAssignedTrainers]);

  // WebSocket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return;

    // Listen for real-time message updates
    const handleNewMessage = (data: any) => {
      if (data.trainerId === selectedTrainer && data.memberId === user?.id) {
        const newMessage: Message = {
          id: data.messageId,
          content: data.content,
          sender: 'assistant',
          timestamp: data.timestamp,
          progress: data.progress,
          score: data.score
        };
        setMessages(prev => [...prev, newMessage]);
        setIsLoading(false);
      }
    };

    // Listen for typing indicators
    const handleTypingStart = (data: any) => {
      if (data.trainerId === selectedTrainer && data.memberId === user?.id) {
        setTypingIndicator(true);
      }
    };

    const handleTypingStop = (data: any) => {
      if (data.trainerId === selectedTrainer && data.memberId === user?.id) {
        setTypingIndicator(false);
      }
    };

    // Listen for progress updates
    const handleProgressUpdate = (data: any) => {
      if (data.trainerId === selectedTrainer && data.memberId === user?.id) {
        // You could show a toast notification here
        console.log('Progress updated:', data);
      }
    };

    on('assistant_message', handleNewMessage);
    on('assistant_typing_start', handleTypingStart);
    on('assistant_typing_stop', handleTypingStop);
    on('progress_updated', handleProgressUpdate);

    return () => {
      off('assistant_message', handleNewMessage);
      off('assistant_typing_start', handleTypingStart);
      off('assistant_typing_stop', handleTypingStop);
      off('progress_updated', handleProgressUpdate);
    };
  }, [socket, isConnected, selectedTrainer, user?.id, on, off]);

  const fetchTrainer = async (id: number) => {
    try {
      const response = await api.trainers.getById(id);
      setTrainer(response.data.data);
    } catch (error) {
      console.error('Error fetching trainer:', error);
    }
  };

  const initializeChat = async (trainerId: number) => {
    try {
      const response = await api.chat.initialize(trainerId);
      if (response.data.success) {
        setSessionId(response.data.data.sessionId);

        // Load conversation history if available
        if (response.data.data.conversationHistory && response.data.data.conversationHistory.length > 0) {
          const formattedMessages = response.data.data.conversationHistory.map((msg: any) => ({
            id: msg.id,
            content: msg.content,
            sender: msg.role === 'user' ? 'user' : 'assistant',
            timestamp: msg.timestamp,
            progress: msg.progress,
            score: msg.score
          }));
          setMessages(formattedMessages);
        } else {
          // Show welcome message if no history
          setMessages([
            {
              id: 1,
              content: 'Hello! I\'m your AI trainer. How can I help you today?',
              sender: 'assistant',
              timestamp: new Date().toISOString()
            }
          ]);
        }
      }
    } catch (error) {
      console.error('Error initializing chat:', error);
      // Fallback to welcome message
      setMessages([
        {
          id: 1,
          content: 'Hello! I\'m your AI trainer. How can I help you today?',
          sender: 'assistant',
          timestamp: new Date().toISOString()
        }
      ]);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !selectedTrainer || !sessionId) return;

    const userMessage: Message = {
      id: Date.now(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputMessage;
    setInputMessage('');
    setIsLoading(true);

    // Add loading message
    const loadingMessage: Message = {
      id: Date.now() + 1,
      content: 'Thinking...',
      sender: 'assistant',
      timestamp: new Date().toISOString(),
      isLoading: true
    };
    setMessages(prev => [...prev, loadingMessage]);

    try {
      const response = await api.chat.sendMessage(selectedTrainer, {
        message: messageToSend,
        sessionId: sessionId
      });

      if (response.data.success) {
        const assistantResponse = response.data.data.response;
        const assistantMessage: Message = {
          id: Date.now() + 2,
          content: assistantResponse.content,
          sender: 'assistant',
          timestamp: new Date().toISOString(),
          progress: assistantResponse.progress,
          score: assistantResponse.score
        };

        setMessages(prev => prev.filter(msg => !msg.isLoading).concat(assistantMessage));
      } else {
        throw new Error(response.data.error?.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Show error message
      const errorMessage: Message = {
        id: Date.now() + 2,
        content: 'Sorry, I encountered an error. Please try again.',
        sender: 'assistant',
        timestamp: new Date().toISOString()
      };

      setMessages(prev => prev.filter(msg => !msg.isLoading).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (!trainerId) {
    return (
      <div className="h-full flex">
        {/* Trainer List */}
        <div className="w-80 bg-white border-r border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Your Trainers</h2>
          </div>
          <div className="p-4">
            {assignedTrainers.length > 0 ? (
              <div className="space-y-2">
                {assignedTrainers.map((trainer) => (
                  <Link
                    key={trainer.id}
                    href={`/member/chat?trainer=${trainer.id}`}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {trainer.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="ml-3 flex-1">
                      <h3 className="font-medium text-gray-900">{trainer.name}</h3>
                      <p className="text-sm text-gray-500 truncate">{trainer.description}</p>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No trainers assigned</p>
              </div>
            )}
          </div>
        </div>

        {/* Empty State */}
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <Bot className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Select a trainer to start chatting</h3>
            <p className="text-gray-500">Choose from your assigned trainers to begin a conversation</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center space-x-3">
          <Link
            href="/member/chat"
            className="text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          {trainer && (
            <>
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {trainer.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h2 className="font-medium text-gray-900">{trainer.name}</h2>
                <p className="text-sm text-gray-500">{trainer.description}</p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-900'
              }`}
            >
              <div className="flex items-start space-x-2">
                {message.sender === 'assistant' && (
                  <div className="flex-shrink-0 mt-1">
                    {message.isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Bot className="h-4 w-4" />
                    )}
                  </div>
                )}
                <div className="flex-1">
                  <p className="text-sm">{message.content}</p>

                  {/* Progress and Score Display */}
                  {message.sender === 'assistant' && (message.progress !== undefined || message.score !== undefined) && (
                    <div className="mt-2 p-2 bg-green-50 rounded text-xs">
                      {message.progress !== undefined && (
                        <div className="flex items-center space-x-2">
                          <span className="text-green-700">Progress:</span>
                          <div className="flex-1 bg-green-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${message.progress}%` }}
                            />
                          </div>
                          <span className="text-green-700 font-medium">{message.progress}%</span>
                        </div>
                      )}
                      {message.score !== undefined && (
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-green-700">Score:</span>
                          <span className="text-green-700 font-medium">{message.score}/100</span>
                        </div>
                      )}
                    </div>
                  )}

                  <p className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>
                {message.sender === 'user' && (
                  <div className="flex-shrink-0 mt-1">
                    <User className="h-4 w-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Typing Indicator */}
        {typingIndicator && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-200 text-gray-900">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-gray-500">AI is typing...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}