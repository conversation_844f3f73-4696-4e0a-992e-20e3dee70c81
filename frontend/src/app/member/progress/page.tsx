'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuthStore } from '@/store/auth';
import { api } from '@/lib/api';
import { Trainer, TrainerSubmodule } from '@/types';
import { useWebSocket } from '@/hooks/useWebSocket';
import ProgressNotification from '@/components/common/ProgressNotification';
import {
  TrendingUp,
  CheckCircle,
  Clock,
  Star,
  BarChart3,
  Target,
  Award,
  Calendar
} from 'lucide-react';

interface ProgressData {
  trainer: Trainer;
  submodules: (TrainerSubmodule & {
    isCompleted: boolean;
    score?: number;
    completedAt?: string;
  })[];
  overallProgress: number;
  averageScore: number;
  totalSubmodules: number;
  completedSubmodules: number;
}



interface ProgressStats {
  totalTrainers: number;
  totalSubmodules: number;
  completedSubmodules: number;
  averageScore: number;
  recentAchievements: Achievement[];
}

interface Achievement {
  id: number;
  title: string;
  description: string;
  earnedAt: string;
  type: 'completion' | 'score' | 'streak';
}

export default function MemberProgressPage() {
  const { user } = useAuthStore();
  const { socket, isConnected, on, off } = useWebSocket();
  const [progressData, setProgressData] = useState<ProgressData[]>([]);
  const [stats, setStats] = useState<ProgressStats>({
    totalTrainers: 0,
    totalSubmodules: 0,
    completedSubmodules: 0,
    averageScore: 0,
    recentAchievements: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [notification, setNotification] = useState<{
    isVisible: boolean;
    type: 'progress' | 'achievement' | 'score';
    title: string;
    message: string;
    progress?: number;
    score?: number;
  }>({
    isVisible: false,
    type: 'progress',
    title: '',
    message: ''
  });


  const fetchProgressData = useCallback(async () => {
    try {
      setIsLoading(true);

      if (user?.id) {
        // Fetch progress data from backend API
        const progressResponse = await api.progress.getByMember(user.id);
        const progressData = progressResponse.data.data || [];

        // Group progress by trainer
        const trainerProgressMap = new Map();

        progressData.forEach((progress: any) => {
          const trainerId = progress.trainer.id;
          if (!trainerProgressMap.has(trainerId)) {
            trainerProgressMap.set(trainerId, {
              trainer: progress.trainer,
              submodules: [],
              overallProgress: 0,
              averageScore: 0,
              totalSubmodules: 0,
              completedSubmodules: 0
            });
          }

          const trainerData = trainerProgressMap.get(trainerId);
          trainerData.submodules.push({
            id: progress.id,
            name: progress.trainer.name, // This might need adjustment based on actual API response
            description: progress.trainer.description,
            isCompleted: progress.isCompleted,
            score: progress.score,
            completedAt: progress.completedAt,
            progress: progress.progress
          });
        });

        // Calculate statistics for each trainer
        const progressResults = Array.from(trainerProgressMap.values()).map((trainerData: any) => {
          const completedCount = trainerData.submodules.filter((s: any) => s.isCompleted).length;
          const totalCount = trainerData.submodules.length;
          const scores = trainerData.submodules.filter((s: any) => s.score !== null).map((s: any) => s.score);
          const averageScore = scores.length > 0 ? scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length : 0;

          return {
            ...trainerData,
            overallProgress: totalCount > 0 ? (completedCount / totalCount) * 100 : 0,
            averageScore,
            totalSubmodules: totalCount,
            completedSubmodules: completedCount
          };
        });

        setProgressData(progressResults);

        // Calculate overall stats
        const totalSubmodules = progressResults.reduce((sum, p) => sum + p.totalSubmodules, 0);
        const completedSubmodules = progressResults.reduce((sum, p) => sum + p.completedSubmodules, 0);
        const averageScore = progressResults.length > 0 ?
          progressResults.reduce((sum, p) => sum + p.averageScore, 0) / progressResults.length : 0;

        // Fetch achievements (you might want to create a separate API endpoint for this)
        const achievements: Achievement[] = [
          {
            id: 1,
            title: 'First Steps',
            description: 'Completed your first module',
            earnedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            type: 'completion'
          }
        ];

        // Add dynamic achievements based on progress
        if (completedSubmodules >= 5) {
          achievements.push({
            id: 2,
            title: 'Dedicated Learner',
            description: 'Completed 5 or more modules',
            earnedAt: new Date().toISOString(),
            type: 'completion'
          });
        }

        if (averageScore >= 90) {
          achievements.push({
            id: 3,
            title: 'High Achiever',
            description: 'Maintained an average score of 90+',
            earnedAt: new Date().toISOString(),
            type: 'score'
          });
        }

        setStats({
          totalTrainers: progressResults.length,
          totalSubmodules,
          completedSubmodules,
          averageScore,
          recentAchievements: achievements
        });
      }

    } catch (error) {
      console.error('Error fetching progress data:', error);
      // Fallback to empty state on error
      setProgressData([]);
      setStats({
        totalTrainers: 0,
        totalSubmodules: 0,
        completedSubmodules: 0,
        averageScore: 0,
        recentAchievements: []
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    fetchProgressData();
  }, [fetchProgressData]);

  // WebSocket event listeners for real-time progress updates
  useEffect(() => {
    if (!socket || !isConnected) return;

    const handleProgressUpdate = (data: any) => {
      if (data.memberId === user?.id) {
        // Refresh progress data
        fetchProgressData();

        // Show notification
        setNotification({
          isVisible: true,
          type: 'progress',
          title: 'Progress Updated!',
          message: `Your progress in ${data.trainerName} has been updated`,
          progress: data.progress
        });
      }
    };

    const handleScoreUpdate = (data: any) => {
      if (data.memberId === user?.id) {
        // Refresh progress data
        fetchProgressData();

        // Show notification
        setNotification({
          isVisible: true,
          type: 'score',
          title: 'New Score!',
          message: `You scored ${data.score} points in ${data.trainerName}`,
          score: data.score
        });
      }
    };

    const handleAchievement = (data: any) => {
      if (data.memberId === user?.id) {
        // Show achievement notification
        setNotification({
          isVisible: true,
          type: 'achievement',
          title: 'Achievement Unlocked!',
          message: data.achievementTitle || 'You earned a new achievement!'
        });
      }
    };

    on('progress_updated', handleProgressUpdate);
    on('score_updated', handleScoreUpdate);
    on('achievement_earned', handleAchievement);

    return () => {
      off('progress_updated', handleProgressUpdate);
      off('score_updated', handleScoreUpdate);
      off('achievement_earned', handleAchievement);
    };
  }, [socket, isConnected, user?.id, on, off, fetchProgressData]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    if (progress >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    if (score >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'completion':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'score':
        return <Star className="h-5 w-5 text-yellow-500" />;
      case 'streak':
        return <Award className="h-5 w-5 text-purple-500" />;
      default:
        return <Target className="h-5 w-5 text-blue-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading progress...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <ProgressNotification
        isVisible={notification.isVisible}
        type={notification.type}
        title={notification.title}
        message={notification.message}
        progress={notification.progress}
        score={notification.score}
        onClose={() => setNotification(prev => ({ ...prev, isVisible: false }))}
      />

      <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Your Learning Progress</h1>
        <p className="text-green-100">Track your progress and achievements across all trainers</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Modules</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalSubmodules}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedSubmodules}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageScore.toFixed(1)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Award className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Achievements</p>
              <p className="text-2xl font-bold text-gray-900">{stats.recentAchievements.length}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Progress by Trainer */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Progress by Trainer</h2>
          </div>
          <div className="p-6">
            {progressData.length > 0 ? (
              <div className="space-y-6">
                {progressData.map((data) => (
                  <div key={data.trainer.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {data.trainer.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{data.trainer.name}</h3>
                          <p className="text-sm text-gray-500">{data.trainer.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {data.completedSubmodules}/{data.totalSubmodules} modules
                        </p>
                        <p className="text-xs text-gray-500">
                          {data.overallProgress.toFixed(1)}% complete
                        </p>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(data.overallProgress)}`}
                          style={{ width: `${data.overallProgress}%` }}
                        />
                      </div>
                    </div>

                    {/* Submodules */}
                    <div className="space-y-2">
                      {data.submodules.map((submodule) => (
                        <div key={submodule.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            {submodule.isCompleted ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Clock className="h-4 w-4 text-gray-400" />
                            )}
                            <span className="text-sm text-gray-900">{submodule.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {submodule.isCompleted && submodule.score && (
                              <span className={`text-sm font-medium ${getScoreColor(submodule.score)}`}>
                                {submodule.score}%
                              </span>
                            )}
                            {submodule.isCompleted && submodule.completedAt && (
                              <span className="text-xs text-gray-500">
                                {formatDate(submodule.completedAt)}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No progress data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Achievements */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Achievements</h2>
          </div>
          <div className="p-6">
            {stats.recentAchievements.length > 0 ? (
              <div className="space-y-4">
                {stats.recentAchievements.map((achievement) => (
                  <div key={achievement.id} className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <div className="flex-shrink-0 mt-1">
                      {getAchievementIcon(achievement.type)}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{achievement.title}</h3>
                      <p className="text-sm text-gray-600">{achievement.description}</p>
                      <p className="text-xs text-gray-400 mt-1 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(achievement.earnedAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No achievements yet</p>
                <p className="text-sm text-gray-400">Complete modules to earn achievements</p>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>
    </>
  );
}